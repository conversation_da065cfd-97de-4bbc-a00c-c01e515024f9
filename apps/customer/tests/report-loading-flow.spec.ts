import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Loading Flow', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('should reset errored components to idle on document load', async ({ page }) => {
    test.setTimeout(300000); // 5 minutes

    let reloadCount = 0;

    // Mock API responses - fail on first document load, succeed on reload
    page.route('**/api/report/**', route => {
      if (reloadCount === 0) {
        // First document load - all calls fail
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Simulated error for testing' })
        });
      } else {
        // After reload - all calls succeed
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            text: '<p>Successfully loaded content after error reset</p>',
            citations: []
          })
        });
      }
    });

    // Track error reset events
    let errorResetDetected = false;
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Found') && text.includes('errored components to reset to idle')) {
        errorResetDetected = true;
        console.log('Error reset detected ✓');
      }
    });

    // Create a document with report components
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report');

    // Wait for components to load and fail
    await page.waitForTimeout(8000);

    // Verify that at least some components are in error state
    // Look for error indicators in the UI
    const errorIndicators = page.locator('svg').filter({ hasText: /error|alert|triangle/i });
    const hasErrors = await errorIndicators.count() > 0;

    if (!hasErrors) {
      // If no visual errors, check console for error messages
      console.log('No visual error indicators found, checking for error states in console...');
    }

    console.log('Components should be in error state, now reloading document...');

    // Reload the document to trigger error reset
    reloadCount++;
    await page.reload();
    await testUtils.waitForEditor();

    // Wait for error reset and new loading to complete
    await page.waitForTimeout(10000);

    // Verify that components have loaded successfully after reset
    const loadedContent = page.locator('text=Successfully loaded content after error reset');
    await expect(loadedContent.first()).toBeVisible({ timeout: 30000 });

    // Verify error reset was detected in console logs
    expect(errorResetDetected).toBe(true);

    console.log('Document reload successfully reset errored components and loaded content ✓');
  });

  test('should notify dependent components when sections start loading', async ({ page }) => {
    test.setTimeout(300000); // 5 minutes

    let sectionStartedLoading = false;
    let groupUpdatedToLoading = false;
    let summaryUpdatedToLoading = false;

    // Mock API responses with delay to observe loading states
    page.route('**/api/report/**', route => {
      const url = route.request().url();
      
      if (url.includes('/section/')) {
        sectionStartedLoading = true;
        console.log('Section API called - section started loading');
        
        // Delay response to allow observation of loading states
        setTimeout(() => {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Section content loaded</p>',
              citations: []
            })
          });
        }, 2000);
      } else if (url.includes('/summarize')) {
        setTimeout(() => {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Summary content generated</p>',
              citations: []
            })
          });
        }, 1000);
      } else {
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            text: '<p>Default content</p>',
            citations: []
          })
        });
      }
    });

    // Monitor console for loading state changes
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Updating parent group') && text.includes('from error to loading')) {
        groupUpdatedToLoading = true;
        console.log('Group updated from error to loading ✓');
      }
      if (text.includes('Updating dependent summary') && text.includes('from error to loading')) {
        summaryUpdatedToLoading = true;
        console.log('Summary updated from error to loading ✓');
      }
    });

    // Create document and wait for initial error state (if any)
    await testUtils.createDocumentFromTemplate('EKO Report');
    await testUtils.waitForEditor();

    // Wait for loading to complete
    await page.waitForTimeout(8000);

    // Verify section started loading
    expect(sectionStartedLoading).toBe(true);

    // Verify components are in loaded state
    const sections = page.locator('.report-section');
    await expect(sections.first()).toBeVisible({ timeout: 15000 });

    const groups = page.locator('.report-group');
    await expect(groups.first()).toBeVisible({ timeout: 15000 });

    const summaries = page.locator('.report-summary');
    if (await summaries.count() > 0) {
      await expect(summaries.first()).toBeVisible({ timeout: 15000 });
    }

    console.log('Dependency notification test completed');
  });

  test('should handle complete loading flow: error -> reset -> loading -> loaded', async ({ page }) => {
    test.setTimeout(300000); // 5 minutes

    const loadingStates: string[] = [];

    // Mock API to simulate error then success
    let callCount = 0;
    page.route('**/api/report/**', route => {
      callCount++;
      
      if (callCount <= 2) {
        // First calls fail
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Initial error' })
        });
      } else {
        // Later calls succeed
        setTimeout(() => {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Content loaded successfully</p>',
              citations: []
            })
          });
        }, 1000);
      }
    });

    // Track state changes
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('status changed from') || text.includes('setting status to')) {
        loadingStates.push(text);
      }
    });

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report');
    await testUtils.waitForEditor();

    // Wait for initial error state
    await page.waitForTimeout(3000);

    // Reload to trigger reset
    await page.reload();
    await testUtils.waitForEditor();

    // Wait for complete loading flow
    await page.waitForTimeout(10000);

    // Verify final loaded state
    const loadedContent = page.locator('text=Content loaded successfully');
    await expect(loadedContent.first()).toBeVisible({ timeout: 30000 });

    // Verify we went through the expected state transitions
    const hasErrorToIdle = loadingStates.some(state => 
      state.includes('error') && state.includes('idle')
    );
    const hasIdleToLoading = loadingStates.some(state => 
      state.includes('idle') && state.includes('loading')
    );
    const hasLoadingToLoaded = loadingStates.some(state => 
      state.includes('loading') && state.includes('loaded')
    );

    console.log('State transitions observed:', loadingStates);
    console.log('Error to idle:', hasErrorToIdle);
    console.log('Idle to loading:', hasIdleToLoading);
    console.log('Loading to loaded:', hasLoadingToLoaded);

    // At minimum, we should see loading to loaded transition
    expect(hasLoadingToLoaded).toBe(true);

    console.log('Complete loading flow test passed ✓');
  });
});
